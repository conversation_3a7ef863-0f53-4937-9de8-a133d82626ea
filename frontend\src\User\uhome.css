#uhome {
    min-height: 100vh;
    background: linear-gradient(rgba(34,34,34,0.7), rgba(34,34,34,0.7)), url("/bookstore-bg.jpg") center/cover no-repeat;
    color: #222;
    font-family: 'Se<PERSON>e UI', '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
    padding: 0;
    margin: 0;
}

.header {
    text-align: center;
    padding-top: 80px;
    padding-bottom: 30px;
    font-size: 2.8rem;
    font-weight: bold;
    color: #fff;
    letter-spacing: 2px;
    background: rgba(20, 184, 166, 0.7);
    margin-bottom: 40px;
}

.book-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 32px;
    padding: 0 40px 40px 40px;
}

.book-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.12);
    width: 220px;
    padding: 20px 16px 16px 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: transform 0.2s, box-shadow 0.2s;
}

.book-card:hover {
    transform: translateY(-6px) scale(1.03);
    box-shadow: 0 8px 24px rgba(0,0,0,0.18);
}

.book-cover {
    width: 120px;
    height: 180px;
    object-fit: cover;
    border-radius: 6px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
}

.book-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    text-align: center;
}

.book-author {
    font-size: 0.95rem;
    color: #7f8c8d;
    margin-bottom: 12px;
    text-align: center;
}

.book-price {
    font-size: 1.05rem;
    color: #27ae60;
    font-weight: 500;
    margin-bottom: 16px;
}

.add-to-cart-btn {
    height: 44px;
    width: 100%;
    background-color: #f39c12;
    border: none;
    color: #fff;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 6px;
    margin-top: auto;
    transition: background 0.2s, opacity 0.2s;
    box-shadow: 0 2px 6px rgba(0,0,0,0.08);
}

.add-to-cart-btn:hover {
    cursor: pointer;
    background-color: #e67e22;
    opacity: 0.92;
}

.cart-icon {
    margin-right: 8px;
    vertical-align: middle;
}

@media (max-width: 900px) {
    .book-list {
        gap: 20px;
        padding: 0 10px 30px 10px;
    }
    .header {
        font-size: 2rem;
        padding-top: 40px;
        padding-bottom: 18px;
    }
}

@media (max-width: 600px) {
    .book-card {
        width: 95vw;
        max-width: 320px;
    }
    .header {
        font-size: 1.3rem;
        padding-top: 24px;
        padding-bottom: 10px;
    }
}