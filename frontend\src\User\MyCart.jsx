import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { getImageUrl, handleImageError } from '../utils/imageUtils';
import './BookStore.css';

const MyCart = () => {
  const [cartItems, setCartItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    loadCartItems();
  }, []);

  const loadCartItems = () => {
    try {
      const cart = JSON.parse(localStorage.getItem('cart') || '[]');
      setCartItems(cart);
    } catch (error) {
      console.error('Error loading cart:', error);
      setCartItems([]);
    } finally {
      setLoading(false);
    }
  };

  const updateQuantity = (itemId, newQuantity) => {
    if (newQuantity < 1) {
      removeFromCart(itemId);
      return;
    }

    const updatedCart = cartItems.map(item =>
      item._id === itemId ? { ...item, quantity: newQuantity } : item
    );
    
    setCartItems(updatedCart);
    localStorage.setItem('cart', JSON.stringify(updatedCart));
  };

  const removeFromCart = (itemId) => {
    const updatedCart = cartItems.filter(item => item._id !== itemId);
    setCartItems(updatedCart);
    localStorage.setItem('cart', JSON.stringify(updatedCart));
  };

  const clearCart = () => {
    if (window.confirm('Are you sure you want to clear your cart?')) {
      setCartItems([]);
      localStorage.removeItem('cart');
    }
  };



  const calculateTotal = () => {
    return cartItems.reduce((total, item) => {
      return total + (parseFloat(item.price) * item.quantity);
    }, 0);
  };

  const proceedToCheckout = () => {
    if (cartItems.length === 0) {
      alert('Your cart is empty!');
      return;
    }
    
    // For now, just show a success message
    alert('Checkout functionality will be implemented soon!');
    // In a real app, you would navigate to checkout page
    // navigate('/checkout');
  };

  if (loading) {
    return (
      <div className="container mt-5">
        <div className="text-center">
          <div className="loading"></div>
          <p>Loading your cart...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mt-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2 className="text-primary">My Cart</h2>
        <button 
          className="btn btn-outline-secondary"
          onClick={() => navigate('/books')}
        >
          Continue Shopping
        </button>
      </div>

      {cartItems.length === 0 ? (
        <div className="text-center py-5">
          <h4 className="text-muted">Your cart is empty</h4>
          <p className="text-muted">Add some books to get started!</p>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/books')}
          >
            Browse Books
          </button>
        </div>
      ) : (
        <>
          <div className="row">
            {cartItems.map(item => (
              <div key={item._id} className="col-12 mb-3">
                <div className="card">
                  <div className="card-body">
                    <div className="row align-items-center">
                      <div className="col-md-2">
                        <img 
                          src={getImageUrl(item.itemImage)} 
                          className="img-fluid rounded"
                          alt={item.title}
                          style={{ maxHeight: '100px', objectFit: 'cover' }}
                          onError={handleImageError}
                        />
                      </div>
                      <div className="col-md-4">
                        <h5 className="card-title text-primary">{item.title}</h5>
                        <p className="card-text text-muted">by {item.author}</p>
                        <p className="text-success font-weight-bold">₹{item.price}</p>
                      </div>
                      <div className="col-md-3">
                        <div className="d-flex align-items-center">
                          <button 
                            className="btn btn-outline-secondary btn-sm"
                            onClick={() => updateQuantity(item._id, item.quantity - 1)}
                          >
                            -
                          </button>
                          <span className="mx-3 font-weight-bold">{item.quantity}</span>
                          <button 
                            className="btn btn-outline-secondary btn-sm"
                            onClick={() => updateQuantity(item._id, item.quantity + 1)}
                          >
                            +
                          </button>
                        </div>
                      </div>
                      <div className="col-md-2">
                        <p className="font-weight-bold text-success">
                          ₹{(parseFloat(item.price) * item.quantity).toFixed(2)}
                        </p>
                      </div>
                      <div className="col-md-1">
                        <button 
                          className="btn btn-outline-danger btn-sm"
                          onClick={() => removeFromCart(item._id)}
                          title="Remove from cart"
                        >
                          ×
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="row mt-4">
            <div className="col-md-8">
              <button 
                className="btn btn-outline-warning"
                onClick={clearCart}
              >
                Clear Cart
              </button>
            </div>
            <div className="col-md-4">
              <div className="card">
                <div className="card-body">
                  <h5 className="card-title">Order Summary</h5>
                  <div className="d-flex justify-content-between">
                    <span>Total Items:</span>
                    <span>{cartItems.reduce((sum, item) => sum + item.quantity, 0)}</span>
                  </div>
                  <div className="d-flex justify-content-between">
                    <span>Subtotal:</span>
                    <span>₹{calculateTotal().toFixed(2)}</span>
                  </div>
                  <hr />
                  <div className="d-flex justify-content-between font-weight-bold">
                    <span>Total:</span>
                    <span className="text-success">₹{calculateTotal().toFixed(2)}</span>
                  </div>
                  <button 
                    className="btn btn-primary w-100 mt-3"
                    onClick={proceedToCheckout}
                  >
                    Proceed to Checkout
                  </button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default MyCart;
