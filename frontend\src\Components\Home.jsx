import React, { useState } from 'react';

// Dummy book data (in real app, fetch from backend)
const books = [
  {
    id: 1,
    title: "The Great Gatsby",
    author: "<PERSON><PERSON>",
    price: 299,
    cover: "/covers/gatsby.jpg",
    description: "A classic novel of the Roaring Twenties.",
  },
  {
    id: 2,
    title: "To Kill a Mockingbird",
    author: "Harper Lee",
    price: 349,
    cover: "/covers/mockingbird.jpg",
    description: "A timeless story of justice and morality.",
  },
  {
    id: 3,
    title: "1984",
    author: "<PERSON>",
    price: 279,
    cover: "/covers/1984.jpg",
    description: "A dystopian novel about totalitarianism.",
  },
  {
    id: 4,
    title: "Pride and Prejudice",
    author: "<PERSON>",
    price: 259,
    cover: "/covers/pride.jpg",
    description: "A romantic classic of manners and marriage.",
  },
  {
    id: 5,
    title: "The Hobbit",
    author: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    price: 399,
    cover: "/covers/hobbit.jpg",
    description: "A fantasy adventure in Middle-earth.",
  },
];

const Home = () => {
  const [cart, setCart] = useState([]);
  const [search, setSearch] = useState("");
  const [showCart, setShowCart] = useState(false);

  const addToCart = (book) => {
    setCart((prev) => {
      const exists = prev.find((b) => b.id === book.id);
      if (exists) {
        return prev.map((b) =>
          b.id === book.id ? { ...b, qty: b.qty + 1 } : b
        );
      }
      return [...prev, { ...book, qty: 1 }];
    });
  };

  const removeFromCart = (id) => {
    setCart((prev) => prev.filter((b) => b.id !== id));
  };

  const updateQty = (id, qty) => {
    setCart((prev) =>
      prev.map((b) =>
        b.id === id ? { ...b, qty: Math.max(1, qty) } : b
      )
    );
  };

  const filteredBooks = books.filter(
    (b) =>
      b.title.toLowerCase().includes(search.toLowerCase()) ||
      b.author.toLowerCase().includes(search.toLowerCase())
  );

  const total = cart.reduce((sum, b) => sum + b.price * b.qty, 0);

  return (
    <div
      style={{
        minHeight: "100vh",
        width: "100%",
        background: "linear-gradient(135deg, #f0fdfa 0%, #fef3c7 100%)",
        fontFamily:
          "'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
        padding: 0,
        margin: 0,
      }}
    >
      <header
        style={{
          width: "100%",
          background: "#14b8a6",
          color: "#fff",
          padding: "1.5rem 0",
          textAlign: "center",
          fontWeight: 700,
          fontSize: "2rem",
          letterSpacing: "2px",
          boxShadow: "0 2px 12px rgba(44,62,80,0.10)",
        }}
      >
        <span role="img" aria-label="book">
          📚
        </span>{" "}
        BookNest Book Store
        <button
          onClick={() => setShowCart((s) => !s)}
          style={{
            float: "right",
            marginRight: "2rem",
            background: "#fff",
            color: "#14b8a6",
            border: "none",
            borderRadius: "999px",
            padding: "0.5em 1.2em",
            fontWeight: 600,
            fontSize: "1rem",
            cursor: "pointer",
            boxShadow: "0 2px 8px rgba(44,62,80,0.10)",
          }}
        >
          🛒 Cart ({cart.length})
        </button>
      </header>
      <main
        style={{
          maxWidth: 1200,
          margin: "2rem auto",
          padding: "0 1rem",
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "2rem",
            flexWrap: "wrap",
            gap: "1rem",
          }}
        >
          <h2
            style={{
              color: "#0f766e",
              fontWeight: 700,
              fontSize: "1.5rem",
              margin: 0,
            }}
          >
            Discover Your Next Read
          </h2>
          <input
            type="text"
            placeholder="Search books or authors..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            style={{
              padding: "0.7em 1.2em",
              borderRadius: "999px",
              border: "1px solid #5eead4",
              fontSize: "1rem",
              width: 260,
              outline: "none",
            }}
          />
        </div>
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(220px, 1fr))",
            gap: "2rem",
          }}
        >
          {filteredBooks.length === 0 && (
            <div
              style={{
                gridColumn: "1/-1",
                textAlign: "center",
                color: "#888",
                fontSize: "1.2rem",
              }}
            >
              No books found.
            </div>
          )}
          {filteredBooks.map((book) => (
            <div
              key={book.id}
              style={{
                background: "#fff",
                borderRadius: 16,
                boxShadow: "0 4px 16px 0 rgba(31, 38, 135, 0.10)",
                padding: "1.5rem 1rem",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                minHeight: 420,
                position: "relative",
              }}
            >
              <img
                src={book.cover}
                alt={book.title}
                style={{
                  width: 120,
                  height: 180,
                  objectFit: "cover",
                  borderRadius: 8,
                  marginBottom: "1rem",
                  boxShadow: "0 2px 8px rgba(44,62,80,0.10)",
                  background: "#f0fdfa",
                }}
              />
              <h3
                style={{
                  fontSize: "1.1rem",
                  fontWeight: 700,
                  color: "#14b8a6",
                  margin: "0.5em 0 0.2em 0",
                  textAlign: "center",
                }}
              >
                {book.title}
              </h3>
              <div
                style={{
                  color: "#555",
                  fontSize: "0.98rem",
                  marginBottom: "0.5em",
                  textAlign: "center",
                }}
              >
                by {book.author}
              </div>
              <div
                style={{
                  color: "#888",
                  fontSize: "0.95rem",
                  marginBottom: "0.7em",
                  textAlign: "center",
                  minHeight: 40,
                }}
              >
                {book.description}
              </div>
              <div
                style={{
                  fontWeight: 700,
                  color: "#0f766e",
                  fontSize: "1.1rem",
                  marginBottom: "1em",
                }}
              >
                ₹{book.price}
              </div>
              <button
                onClick={() => addToCart(book)}
                style={{
                  padding: "0.6em 1.2em",
                  borderRadius: "9999px",
                  border: "none",
                  background: "#14b8a6",
                  color: "#fff",
                  fontSize: "1em",
                  fontWeight: 600,
                  cursor: "pointer",
                  transition: "background 0.2s, transform 0.2s",
                  boxShadow: "0 2px 8px rgba(44,62,80,0.10)",
                  width: "80%",
                  maxWidth: 180,
                  margin: "0 auto",
                }}
              >
                Add to Cart
              </button>
            </div>
          ))}
        </div>
      </main>
      {showCart && (
        <div
          style={{
            position: "fixed",
            top: 0,
            right: 0,
            width: 360,
            maxWidth: "90vw",
            height: "100vh",
            background: "#fff",
            boxShadow: "-4px 0 24px rgba(44,62,80,0.15)",
            zIndex: 100,
            padding: "2rem 1.5rem 1.5rem 1.5rem",
            overflowY: "auto",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <button
            onClick={() => setShowCart(false)}
            style={{
              alignSelf: "flex-end",
              background: "none",
              border: "none",
              fontSize: "1.5rem",
              color: "#14b8a6",
              cursor: "pointer",
              marginBottom: "1rem",
            }}
            aria-label="Close cart"
          >
            ×
          </button>
          <h2
            style={{
              color: "#0f766e",
              fontWeight: 700,
              fontSize: "1.3rem",
              marginBottom: "1.2rem",
            }}
          >
            Your Cart
          </h2>
          {cart.length === 0 ? (
            <div style={{ color: "#888", textAlign: "center" }}>
              Your cart is empty.
            </div>
          ) : (
            <>
              <ul style={{ listStyle: "none", padding: 0, margin: 0 }}>
                {cart.map((item) => (
                  <li
                    key={item.id}
                    style={{
                      display: "flex",
                      alignItems: "center",
                      marginBottom: "1.2rem",
                      borderBottom: "1px solid #eee",
                      paddingBottom: "1rem",
                    }}
                  >
                    <img
                      src={item.cover}
                      alt={item.title}
                      style={{
                        width: 48,
                        height: 70,
                        objectFit: "cover",
                        borderRadius: 6,
                        marginRight: "1rem",
                        background: "#f0fdfa",
                      }}
                    />
                    <div style={{ flex: 1 }}>
                      <div
                        style={{
                          fontWeight: 600,
                          color: "#14b8a6",
                          fontSize: "1rem",
                        }}
                      >
                        {item.title}
                      </div>
                      <div
                        style={{
                          color: "#555",
                          fontSize: "0.95rem",
                          marginBottom: "0.3em",
                        }}
                      >
                        by {item.author}
                      </div>
                      <div
                        style={{
                          color: "#0f766e",
                          fontWeight: 700,
                          fontSize: "1rem",
                        }}
                      >
                        ₹{item.price}
                      </div>
                      <div style={{ marginTop: "0.5em" }}>
                        <button
                          onClick={() =>
                            updateQty(item.id, item.qty - 1)
                          }
                          style={{
                            border: "none",
                            background: "#f0fdfa",
                            color: "#14b8a6",
                            borderRadius: "50%",
                            width: 28,
                            height: 28,
                            fontWeight: 700,
                            fontSize: "1.1rem",
                            cursor: "pointer",
                            marginRight: 6,
                          }}
                          disabled={item.qty <= 1}
                        >
                          -
                        </button>
                        <span
                          style={{
                            fontWeight: 600,
                            fontSize: "1rem",
                            margin: "0 8px",
                          }}
                        >
                          {item.qty}
                        </span>
                        <button
                          onClick={() =>
                            updateQty(item.id, item.qty + 1)
                          }
                          style={{
                            border: "none",
                            background: "#f0fdfa",
                            color: "#14b8a6",
                            borderRadius: "50%",
                            width: 28,
                            height: 28,
                            fontWeight: 700,
                            fontSize: "1.1rem",
                            cursor: "pointer",
                            marginLeft: 6,
                          }}
                        >
                          +
                        </button>
                        <button
                          onClick={() => removeFromCart(item.id)}
                          style={{
                            border: "none",
                            background: "none",
                            color: "#dc2626",
                            fontWeight: 700,
                            fontSize: "1.1rem",
                            marginLeft: 16,
                            cursor: "pointer",
                          }}
                          title="Remove from cart"
                        >
                          🗑️
                        </button>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
              <div
                style={{
                  fontWeight: 700,
                  color: "#0f766e",
                  fontSize: "1.1rem",
                  marginTop: "1.5rem",
                  textAlign: "right",
                }}
              >
                Total: ₹{total}
              </div>
              <button
                style={{
                  marginTop: "1.5rem",
                  width: "100%",
                  padding: "0.8em 0",
                  borderRadius: "9999px",
                  border: "none",
                  background: "#14b8a6",
                  color: "#fff",
                  fontSize: "1.1em",
                  fontWeight: 700,
                  cursor: "pointer",
                  transition: "background 0.2s, transform 0.2s",
                  boxShadow: "0 2px 8px rgba(44,62,80,0.10)",
                }}
                onClick={() => alert("Checkout is not implemented in this demo.")}
              >
                Proceed to Checkout
              </button>
            </>
          )}
        </div>
      )}
      <footer
        style={{
          width: "100%",
          background: "#14b8a6",
          color: "#fff",
          padding: "1rem 0",
          textAlign: "center",
          fontSize: "1rem",
          marginTop: "2rem",
          letterSpacing: "1px",
        }}
      >
        © {new Date().getFullYear()} BookNest Book Store. All rights reserved.
      </footer>
    </div>
  );
};

export default Home;
