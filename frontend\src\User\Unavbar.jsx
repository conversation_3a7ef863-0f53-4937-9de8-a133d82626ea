// src/components/Navbar.js

import React from 'react';
import { Navbar, Nav, Container, NavDropdown } from 'react-bootstrap';
import { Link, useNavigate } from "react-router-dom";

const Unavbar = () => {
  const navigate = useNavigate();
  const user = localStorage.getItem('user');
  let userName = '';
  if (user) {
    try {
      userName = JSON.parse(user).name;
    } catch (e) {
      userName = '';
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('user');
    navigate('/');
  };

  return (
    <Navbar bg="primary" variant="dark" expand="lg" style={{ backgroundColor: "#14b8a6" }}>
      <Container>
        <Navbar.Brand>
          <Link to='/uhome' style={{ color: 'white', textDecoration: "none", fontWeight: "bold", fontSize: "1.5rem" }}>
            <img
              src="/bookstore-logo.png"
              alt="BookStore Logo"
              style={{ width: 40, height: 40, marginRight: 10, verticalAlign: "middle" }}
              onError={e => { e.target.style.display = 'none'; }}
            />
            BookStore
          </Link>
        </Navbar.Brand>
        <Navbar.Toggle aria-controls="basic-navbar-nav" />
        <Navbar.Collapse id="basic-navbar-nav">
          <Nav className="ms-auto" style={{ alignItems: "center" }}>
            <Link to="/uhome" style={{ padding: "10px", color: "white", textDecoration: "none" }}>Home</Link>
            <Link to="/books" style={{ padding: "10px", color: "white", textDecoration: "none" }}>Books</Link>
            <Link to="/mycart" style={{ padding: "10px", color: "white", textDecoration: "none" }}>My Cart</Link>
            <Link to="/wishlist" style={{ padding: "10px", color: "white", textDecoration: "none" }}>Wishlist</Link>
            <Link to="/myorders-new" style={{ padding: "10px", color: "white", textDecoration: "none" }}>My Orders</Link>
            <NavDropdown title={
              <span style={{ color: "white" }}>
                <i className="bi bi-person-circle" style={{ marginRight: 5 }}></i>
                {userName ? userName : "User"}
              </span>
            } id="user-nav-dropdown" align="end">
              <NavDropdown.Item onClick={handleLogout}>Logout</NavDropdown.Item>
            </NavDropdown>
          </Nav>
        </Navbar.Collapse>
      </Container>
    </Navbar>
  );
};

export default Unavbar;
