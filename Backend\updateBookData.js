const mongoose = require('mongoose');
require('./db/config');
const items = require('./db/Seller/Additem');

// Mapping of image filenames to correct book data
const correctBookData = {
  "1700630708206-1979210[1].jpg": {
    title: "The Alchemist",
    author: "<PERSON>",
    genre: "Fiction",
    description: "A philosophical novel about a young shepherd's journey to find treasure and discover his personal legend.",
    price: "299"
  },
  "1700631424222-18505809[1].jpg": {
    title: "The Kite Runner",
    author: "<PERSON><PERSON><PERSON>",
    genre: "Historical Fiction",
    description: "A powerful story of friendship, betrayal, and redemption set against the backdrop of Afghanistan's tumultuous history.",
    price: "349"
  },
  "1700632005607-136251[1].jpg": {
    title: "Life of Pi",
    author: "Yann <PERSON>",
    genre: "Adventure Fiction",
    description: "An extraordinary tale of survival and faith about a boy stranded on a lifeboat with a Bengal tiger.",
    price: "279"
  },
  "1700632074778-136251.jpg": {
    title: "The Book Thief",
    author: "<PERSON>",
    genre: "Historical Fiction",
    description: "A haunting tale narrated by <PERSON> about a young girl living in Nazi Germany who steals books.",
    price: "259"
  },
  "1700632515790-29502358[1].jpg": {
    title: "The Fault in Our Stars",
    author: "John Green",
    genre: "Young Adult Fiction",
    description: "A heart-wrenching love story between two teenagers who meet in a cancer support group.",
    price: "289"
  },
  "1700632736939-30186948[1].jpg": {
    title: "The Hunger Games",
    author: "Suzanne Collins",
    genre: "Dystopian Fiction",
    description: "A thrilling dystopian novel about a girl who volunteers to take her sister's place in a deadly competition.",
    price: "399"
  },
  "1700632928170-80830635[1].jpg": {
    title: "The Da Vinci Code",
    author: "Dan Brown",
    genre: "Mystery Thriller",
    description: "A gripping mystery that follows symbologist Robert Langdon as he investigates a murder in the Louvre.",
    price: "599"
  },
  "1700633112352-42983957[1].jpg": {
    title: "The Girl with the Dragon Tattoo",
    author: "Stieg Larsson",
    genre: "Crime Thriller",
    description: "A gripping crime thriller about a journalist and a hacker investigating a wealthy family's dark secrets.",
    price: "449"
  },
  "1700633869849-122765395[1].jpg": {
    title: "Gone Girl",
    author: "Gillian Flynn",
    genre: "Psychological Thriller",
    description: "A psychological thriller about a marriage gone terribly wrong when a wife disappears on her anniversary.",
    price: "379"
  },
  "1700638048715-61111298[1].jpg": {
    title: "The Silent Patient",
    author: "Alex Michaelides",
    genre: "Psychological Thriller",
    description: "A gripping psychological thriller about a woman who refuses to speak after allegedly murdering her husband.",
    price: "329"
  },
  "1700643491707-63331415[1].jpg": {
    title: "Where the Crawdads Sing",
    author: "Delia Owens",
    genre: "Mystery Fiction",
    description: "A coming-of-age mystery about a girl who grows up isolated in the marshes of North Carolina.",
    price: "359"
  },
  "1700721610720-29502358[1].jpg": {
    title: "The Seven Husbands of Evelyn Hugo",
    author: "Taylor Jenkins Reid",
    genre: "Historical Fiction",
    description: "A captivating novel about a reclusive Hollywood icon who finally decides to tell her life story.",
    price: "319"
  },
  "1742294964528-images.jpeg": {
    title: "Educated",
    author: "Tara Westover",
    genre: "Memoir",
    description: "A powerful memoir about education, family, and the struggle between loyalty and independence.",
    price: "389"
  }
};

async function updateBookData() {
  try {
    console.log('🔄 Starting book data update...');
    
    // Get all books from database
    const books = await items.find();
    console.log(`📚 Found ${books.length} books in database`);
    
    let updatedCount = 0;
    
    for (const book of books) {
      const correctData = correctBookData[book.itemImage];
      
      if (correctData) {
        // Update the book with correct data
        await items.findByIdAndUpdate(book._id, {
          title: correctData.title,
          author: correctData.author,
          genre: correctData.genre,
          description: correctData.description,
          price: correctData.price
        });
        
        console.log(`✅ Updated: ${book.itemImage} -> ${correctData.title} by ${correctData.author}`);
        updatedCount++;
      } else {
        console.log(`⚠️  No mapping found for: ${book.itemImage}`);
      }
    }
    
    console.log(`\n🎉 Update complete! Updated ${updatedCount} out of ${books.length} books.`);
    
    // Display updated books
    console.log('\n📖 Updated book list:');
    const updatedBooks = await items.find();
    updatedBooks.forEach(book => {
      console.log(`- ${book.title} by ${book.author} (${book.itemImage})`);
    });
    
  } catch (error) {
    console.error('❌ Error updating book data:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the update
updateBookData();
